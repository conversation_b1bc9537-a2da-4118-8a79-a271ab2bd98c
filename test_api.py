#!/usr/bin/env python3
"""
API测试脚本
用于测试AI医疗小助手后端API的基本功能
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5000/api/v1"
TEST_USER = {
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
}

class APITester:
    def __init__(self, base_url):
        self.base_url = base_url
        self.access_token = None
        self.session = requests.Session()
    
    def test_user_registration(self):
        """测试用户注册"""
        print("🔍 测试用户注册...")
        url = f"{self.base_url}/auth/register"
        
        response = self.session.post(url, json=TEST_USER)
        
        if response.status_code == 201:
            data = response.json()
            self.access_token = data.get('access_token')
            print("✅ 用户注册成功")
            print(f"   用户ID: {data['user']['id']}")
            print(f"   用户名: {data['user']['username']}")
            return True
        elif response.status_code == 409:
            print("⚠️  用户已存在，尝试登录...")
            return self.test_user_login()
        else:
            print(f"❌ 用户注册失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    
    def test_user_login(self):
        """测试用户登录"""
        print("🔍 测试用户登录...")
        url = f"{self.base_url}/auth/login"
        
        response = self.session.post(url, json={
            "username": TEST_USER["username"],
            "password": TEST_USER["password"]
        })
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get('access_token')
            print("✅ 用户登录成功")
            print(f"   用户名: {data['user']['username']}")
            return True
        else:
            print(f"❌ 用户登录失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    
    def test_get_profile(self):
        """测试获取用户信息"""
        print("🔍 测试获取用户信息...")

        if not self.access_token:
            print("❌ 没有访问令牌，无法测试")
            return False

        url = f"{self.base_url}/auth/profile"
        headers = {"Authorization": f"Bearer {self.access_token}"}

        response = self.session.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            print("✅ 获取用户信息成功")
            print(f"   用户信息: {data['user']}")
            return True
        else:
            print(f"❌ 获取用户信息失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            print(f"   Token: {self.access_token[:20]}..." if self.access_token else "   Token: None")
            return False
    
    def test_create_conversation(self):
        """测试创建对话"""
        print("🔍 测试创建对话...")
        url = f"{self.base_url}/conversations"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        response = self.session.post(url, json={
            "title": "健康咨询测试"
        }, headers=headers)
        
        if response.status_code == 201:
            data = response.json()
            conversation_id = data['conversation']['id']
            print("✅ 创建对话成功")
            print(f"   对话ID: {conversation_id}")
            return conversation_id
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    
    def test_send_message(self, conversation_id):
        """测试发送消息"""
        print("🔍 测试发送消息...")
        url = f"{self.base_url}/conversations/{conversation_id}/messages"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        test_message = "我最近经常头痛，请问可能是什么原因？"
        
        response = self.session.post(url, json={
            "content": test_message
        }, headers=headers)
        
        if response.status_code in [201, 202]:
            data = response.json()
            print("✅ 发送消息成功")
            print(f"   用户消息: {data['user_message']['content']}")
            if data.get('ai_message'):
                print(f"   AI回复: {data['ai_message']['content'][:100]}...")
            else:
                print("   AI服务暂时不可用")
            return True
        else:
            print(f"❌ 发送消息失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    
    def test_get_conversations(self):
        """测试获取对话列表"""
        print("🔍 测试获取对话列表...")
        url = f"{self.base_url}/conversations"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        response = self.session.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取对话列表成功")
            print(f"   对话数量: {len(data['conversations'])}")
            for conv in data['conversations'][:3]:  # 显示前3个对话
                print(f"   - {conv['title']} (ID: {conv['id']})")
            return True
        else:
            print(f"❌ 获取对话列表失败: {response.status_code}")
            return False
    
    def test_get_messages(self, conversation_id):
        """测试获取消息历史"""
        print("🔍 测试获取消息历史...")
        url = f"{self.base_url}/conversations/{conversation_id}/messages"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        response = self.session.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取消息历史成功")
            print(f"   消息数量: {len(data['messages'])}")
            for msg in data['messages']:
                print(f"   - {msg['role']}: {msg['content'][:50]}...")
            return True
        else:
            print(f"❌ 获取消息历史失败: {response.status_code}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API功能测试...\n")
        
        # 测试认证功能
        if not self.test_user_registration():
            return False
        
        time.sleep(0.5)
        
        if not self.test_get_profile():
            return False
        
        time.sleep(0.5)
        
        # 测试对话功能
        conversation_id = self.test_create_conversation()
        if not conversation_id:
            return False
        
        time.sleep(0.5)
        
        if not self.test_send_message(conversation_id):
            return False
        
        time.sleep(0.5)
        
        if not self.test_get_conversations():
            return False
        
        time.sleep(0.5)
        
        if not self.test_get_messages(conversation_id):
            return False
        
        print("\n🎉 所有测试完成！")
        return True

def main():
    """主函数"""
    print("AI医疗小助手后端API测试")
    print("=" * 40)
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL.replace('/api/v1', '')}/")
        print(f"✅ 服务运行状态检查通过")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务，请确保后端服务已启动")
        print("   启动命令: python app.py")
        return
    
    # 运行测试
    tester = APITester(BASE_URL)
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 所有API测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
