#!/usr/bin/env python3
"""
测试MySQL连接和API功能
"""

import requests
import json

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        from app import create_app, db
        from app.models.user import User
        
        app = create_app()
        with app.app_context():
            # 测试数据库连接
            with db.engine.connect() as conn:
                result = conn.execute(db.text("SELECT 1")).scalar()
            print("✅ MySQL数据库连接成功")
            
            # 测试表是否存在
            user_count = User.query.count()
            print(f"✅ 用户表查询成功，当前用户数: {user_count}")
            
            return True
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_api():
    """测试API功能"""
    try:
        # 测试注册
        url = "http://localhost:5000/api/v1/auth/register"
        data = {
            "username": "mysqltest",
            "email": "<EMAIL>", 
            "password": "password123"
        }
        
        response = requests.post(url, json=data)
        print(f"注册API状态码: {response.status_code}")
        
        if response.status_code in [201, 409]:  # 201创建成功，409用户已存在
            print("✅ API功能正常")
            return True
        else:
            print(f"❌ API异常: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    print("🔍 测试MySQL数据库和API功能")
    print("=" * 40)
    
    # 测试数据库连接
    if test_mysql_connection():
        print("✅ 数据库测试通过")
    else:
        print("❌ 数据库测试失败")
        return
    
    # 测试API
    if test_api():
        print("✅ API测试通过")
    else:
        print("❌ API测试失败")
        return
    
    print("=" * 40)
    print("🎉 所有测试通过！MySQL配置成功！")

if __name__ == "__main__":
    main()
