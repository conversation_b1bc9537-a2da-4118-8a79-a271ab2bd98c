#!/usr/bin/env python3
"""
JWT测试脚本
"""

from app import create_app
from flask_jwt_extended import create_access_token, decode_token
import jwt

def test_jwt():
    app = create_app()
    
    with app.app_context():
        # 创建token
        user_id = 1
        token = create_access_token(identity=user_id)
        print(f"创建的Token: {token}")
        
        # 尝试解码token
        try:
            decoded = decode_token(token)
            print(f"解码成功: {decoded}")
        except Exception as e:
            print(f"解码失败: {e}")
        
        # 检查配置
        print(f"JWT Secret Key: {app.config['JWT_SECRET_KEY']}")
        print(f"JWT Algorithm: {app.config.get('JWT_ALGORITHM', 'HS256')}")

if __name__ == "__main__":
    test_jwt()
