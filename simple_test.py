#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import requests
import json

BASE_URL = "http://localhost:5000/api/v1"

def test_registration():
    """测试用户注册"""
    print("测试用户注册...")
    
    url = f"{BASE_URL}/auth/register"
    data = {
        "username": "simpletest",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            return result.get('access_token')
        elif response.status_code == 409:
            print("用户已存在，尝试登录...")
            return test_login()
        else:
            print("注册失败")
            return None
            
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def test_login():
    """测试用户登录"""
    print("测试用户登录...")
    
    url = f"{BASE_URL}/auth/login"
    data = {
        "username": "simpletest",
        "password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            return result.get('access_token')
        else:
            print("登录失败")
            return None
            
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def test_profile(token):
    """测试获取用户信息"""
    print("测试获取用户信息...")
    
    url = f"{BASE_URL}/auth/profile"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"请求异常: {e}")
        return False

def main():
    print("=== 简单API测试 ===")
    
    # 测试注册/登录
    token = test_registration()
    if not token:
        print("❌ 无法获取访问令牌")
        return
    
    print(f"✅ 获取到访问令牌: {token[:20]}...")
    
    # 测试获取用户信息
    if test_profile(token):
        print("✅ 所有测试通过！")
    else:
        print("❌ 用户信息测试失败")

if __name__ == "__main__":
    main()
